import React from 'react';
import axios from 'axios';
import <PERSON><PERSON> from 'mockjs';
import { OrganizationSelector } from '@roo/roo'

// 模拟数据，包含不同层级的组织结构
const mockDataByLevel = {
    1: {
        "list": [
            {
                "orgType": 20,
                "editable": true,
                "id": 2132,
                "unreal": 0,
                "parentId": -100,
                "level": 1,
                "source": 1,
                "isSelected": false,
                "isLeaf": 0,
                "name": "总部",
                "bizType": 1,
                "childrenList": null,
                "isManager": false,
                "orgLevel": null
            }
        ],
        "isHq": true
    },
    2: {
        "list": [
            {
                "orgType": 21,
                "editable": true,
                "id": 2133,
                "unreal": 0,
                "parentId": 2132,
                "level": 2,
                "source": 1,
                "isSelected": false,
                "isLeaf": 0,
                "name": "华北区域",
                "bizType": 1,
                "childrenList": null,
                "isManager": false,
                "orgLevel": null
            },
            {
                "orgType": 21,
                "editable": true,
                "id": 2134,
                "unreal": 0,
                "parentId": 2132,
                "level": 2,
                "source": 1,
                "isSelected": false,
                "isLeaf": 0,
                "name": "华南区域",
                "bizType": 1,
                "childrenList": null,
                "isManager": false,
                "orgLevel": null
            }
        ],
        "isHq": false
    },
    3: {
        "list": [
            {
                "orgType": 22,
                "editable": true,
                "id": 2135,
                "unreal": 0,
                "parentId": 2133,
                "level": 3,
                "source": 1,
                "isSelected": false,
                "isLeaf": 1,
                "name": "北京分公司",
                "bizType": 1,
                "childrenList": null,
                "isManager": false,
                "orgLevel": null
            },
            {
                "orgType": 22,
                "editable": true,
                "id": 2136,
                "unreal": 0,
                "parentId": 2133,
                "level": 3,
                "source": 1,
                "isSelected": false,
                "isLeaf": 1,
                "name": "天津分公司",
                "bizType": 1,
                "childrenList": null,
                "isManager": false,
                "orgLevel": null
            }
        ],
        "isHq": false
    }
};

// Mock 接口，根据 level 参数返回不同的数据
Mock.mock('/pidservice/level-mock', 'get', (options: any) => {
    const params = new URLSearchParams(options.url.split('?')[1]);
    const level = params.get('level');
    const parentId = params.get('parentId');
    
    console.log('PidService 接收到的参数:', {
        level: level,
        parentId: parentId,
        allParams: Object.fromEntries(params.entries())
    });
    
    // 根据 level 返回对应层级的数据
    const levelNum = level ? parseInt(level) + 1 : 1;
    const data = mockDataByLevel[levelNum as keyof typeof mockDataByLevel] || { list: [], isHq: false };
    
    return {
        code: 0,
        message: '获取成功',
        data: data
    };
});

const PidServiceWithLevel = () => {
    // 自定义 pidService，可以接收到 level 参数
    const getDataWithLevel = async (params: any) => {
        console.log('pidService 接收到的完整参数:', params);
        
        // 这里可以根据 level 参数进行不同的业务逻辑处理
        const { level, parentId, ...otherParams } = params;
        
        const data: any = await axios.get('/pidservice/level-mock', {
            params: {
                level,
                parentId,
                ...otherParams
            }
        });
        
        return data;
    };

    return (
        <div>
            <h3>使用 pidService 获取点击节点的 level 参数示例</h3>
            <p>打开浏览器控制台查看 pidService 接收到的参数，包括 level 信息</p>
            <OrganizationSelector
                placeholder="选择组织结构（支持 level 参数）"
                pidUrl="https://yapi.sankuai.com/mock/2959/uicomponent/api/orgs/getByPid"
                searchUrl="https://yapi.sankuai.com/mock/2959/uicomponent/api/orgs/search"
                params={{
                    sources: '1_4_5_6',
                    backtrackOrgType: 20,
                }}
                multiple
                pidService={getDataWithLevel}
                showSearch={false}
            />
        </div>
    );
};

export default PidServiceWithLevel;
