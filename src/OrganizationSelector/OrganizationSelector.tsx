import * as React from 'react';
import PropTypes from 'prop-types';
import axios from 'axios';
import cloneDeep from 'lodash/cloneDeep';
import isEqual from 'lodash/isEqual';
import difference from 'lodash/difference';
import { isFunction } from '@utiljs/is';
import classnames from 'classnames';
import { placementEnum, innerPlacement, placementTypes } from './utils/types';
import OrgSelect from './Select';
import { OrganizationSelectorProps, OrganizationSelectorState } from './interface';
import { GlobalConfigContext } from '@roo/roo/ConfigProvider';
import Input from '@roo/roo/Input';
import Button from '@roo/roo/Button';
import CitySelector from '@roo/roo/CitySelector';
import locale from '@roo/roo/locale';
import Popper from '@roo/roo/core/Popper';
import Toast from '@roo/roo/Toast';

const { Reference, Manager, PopperPortal } = Popper
class OrganizationSelector extends React.Component<OrganizationSelectorProps, OrganizationSelectorState> {
    static propTypes = {
        className: PropTypes.string,
        style: PropTypes.object,
        popupClass: PropTypes.string,
        popupStyle: PropTypes.object,
        disabled: PropTypes.bool,
        placeholder: PropTypes.string,
        // size: PropTypes.oneOf(['large', 'normal', 'small', 'mini']),
        styleType: PropTypes.oneOf(['line', 'plaintext']),
        multiple: PropTypes.bool,
        showSearch: PropTypes.bool,
        pidUrl: PropTypes.string,
        searchUrl: PropTypes.string,
        params: PropTypes.object,
        method: PropTypes.oneOf(['get', 'post']),
        config: PropTypes.object,
        maxNum: PropTypes.number,
        defaultLevelId: PropTypes.number,
        onFail: PropTypes.func,
        pidService: PropTypes.func,
        defaultValue: PropTypes.oneOfType([
            PropTypes.number,
            PropTypes.array,
        ]),
        cascade: PropTypes.bool,
        placement: PropTypes.oneOf(innerPlacement),
        searchPlaceholder: PropTypes.string,
        allowClear: PropTypes.bool,
        disablePortal: PropTypes.bool,
        zIndex: PropTypes.number,
        flip: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
        onConfirm: PropTypes.func,
        onAllowClear: PropTypes.func,
        filterFn: PropTypes.func,
        onMountedFetchFn: PropTypes.func,
        onDelete: PropTypes.func,
        popupContainer: PropTypes.oneOfType([PropTypes.instanceOf(Element), PropTypes.func]),
        searchDisablePortal: PropTypes.bool,
        asyncFetchTree: PropTypes.bool,
        searchTransformData: PropTypes.func,
        renderMenu: PropTypes.func,
        renderMenuItem: PropTypes.func,
        onValueChange: PropTypes.func,
    }

    static contextType?: React.Context<any> | undefined = GlobalConfigContext;

    static defaultProps = {
        className: '',
        style: {},
        popupClass: '',
        popupStyle: {},
        // size: 'normal',
        multiple: false,
        placeholder: null,
        showSearch: true,
        disabled: false,
        pidUrl: '/uicomponent/api/orgs/getByPid',
        searchUrl: '/uicomponent/api/orgs/search',
        method: 'get',
        maxNum: null,
        cascade: true,
        placement: undefined,
        searchPlaceholder: undefined,
        flip: true,
        allowClear: false,
        onAllowClear: undefined,
        disablePortal: false,
        zIndex: 1000,
        onFail: () => {
            Toast.fail({
                title: locale.lng('OrganizationPicker.prompt'),
                children: locale.lng('OrganizationPicker.fail'),
                theme: 'light',
                position: 'center',
                duration: 3000,
            });
        },
        onMountedFetchFn: undefined,
        popupContainer: undefined,
        defaultLevelId: undefined,
        onDelete: undefined,
        searchDisablePortal: false,
        asyncFetchTree: true,
    };

    // select 框体
    private selectRef: HTMLElement | null = null;

    private inputRef: React.RefObject<HTMLInputElement> = React.createRef();

    private citySelectorRef: React.RefObject<any> = React.createRef();

    private orgSelectRef: React.RefObject<any> = React.createRef();

    private hasInitialFetch: boolean = false;

    constructor(props: OrganizationSelectorProps) {
        super(props);
        this.state = {
            visible: false,
            showPopover: false,
            inputValue: '',
            btnDisabled: false,
            organizationOptions: [], //CitySelector的options
            searchedValueOptions: [], //CitySelector的valueOptions
            //selectedIds和searchedIds分开管理是因为方便清除和选择搜索框选择的id
            selectedIds: [], //选中的id
            searchedIds: [], //搜索框搜索的id
            resetIds: [], //reset时的id
            isOrgDidMount: false, //只在true时在componentDidUpdate中判断是否需要更新value和其他数据,防止初始化调用多次
            isConfirm: false,
            loading: false,
            minLevel: null,
        };
    }

    componentDidMount() {
        if (this.hasInitialFetch) {
            return;
        }
        this.hasInitialFetch = true;

        const { defaultValue, multiple, value, onMountedFetchFn } = this.props;
        let defaultIds: any = [];
        let resetIds: number[] = [];
        if (multiple) {
            if (value || value === 0) {
                defaultIds = value;
            } else if (defaultValue) {
                defaultIds = defaultValue;
            }
        } else {
            if (value || value === 0) {
                defaultIds = [value];
            } else if (defaultValue) {
                defaultIds = [defaultValue];
            }
        }
        if (defaultValue) {
            resetIds = multiple ? defaultValue as number[] : [defaultValue as number];
        }
        if (resetIds) {
            this.setState({
                resetIds,
            });
        }
        const params: any = { ids: defaultIds, isDidMount: true };
        if (onMountedFetchFn && isFunction(onMountedFetchFn)) {
            params.onMountedFetchFn = onMountedFetchFn;
        }

        const fn = async () => {
            this.setState({ loading: true });
            await this.getOrgOptionsById(params);
            this.setState({ loading: false });
        };

        fn();
    }

    componentDidUpdate(prevProps: OrganizationSelectorProps) {
        const { value: nextValue, multiple, defaultValue } = this.props;
        const { value: prevValue } = prevProps;
        const { selectedIds, isOrgDidMount, searchedIds } = this.state;
        
        // 判断是否由外部手动更改value,如有则更新数据
        if ('value' in this.props && isOrgDidMount && !searchedIds.length && prevValue !== nextValue) {
            let isSameValue = true;
            
            if (multiple) {
                const nextValueArray = Array.isArray(nextValue) ? nextValue : [];
                const isOnlyPropsValue = nextValueArray?.length && !selectedIds?.length;
                const isOnlySelected = selectedIds?.length && !nextValueArray?.length;
                const isAllValue = nextValueArray?.length && selectedIds?.length;
                
                if (defaultValue && selectedIds?.length && !nextValue) {
                    // 如果有defaultValue,selectedIds,没有value情况下代表初始化,不进行请求
                    isSameValue = true;
                } else if (isOnlyPropsValue || isOnlySelected || isAllValue) {
                    isSameValue = isEqual(nextValue, selectedIds);
                }
            } else {
                const isOnlyPropsValue = nextValue !== undefined && !selectedIds.length;
                const isOnlySelected = selectedIds?.length && nextValue === undefined;
                const isAllValue = nextValue !== undefined && selectedIds?.length;
                if (defaultValue && selectedIds?.length && !nextValue) {
                    //如果有defaultValue,selectedIds,没有value情况下代表初始化,不进行请求
                    isSameValue = true;
                } else if (isOnlyPropsValue || isOnlySelected || isAllValue) {
                    isSameValue = nextValue === selectedIds[0];
                }
            }
            if (!isSameValue) {
                let ids: number[] = [];
                if (nextValue !== undefined) {
                    ids = multiple ? (nextValue as number[]) : [nextValue as number];
                }
                this.getOrgOptionsById({ ids, isDidMount: true });
            }
        }
    }

    getPlacement = () => {
        const { placement } = this.props;
        if (placement) {
            return placementEnum[placement as placementTypes];
        } else {
            return this.context.direction === 'RTL' ? placementEnum.bottomRight : placementEnum.bottomLeft;
        }
    }


    fetchData = async (option: any) => new Promise(async (resolve) => {
        const data = await this.getOrgOptionsById({ parentId: option.id, level: option.level });
        resolve(data);
    })

    handleResetClick = (isNeedReSearch?: boolean) => {
        const { resetIds } = this.state;
        this.orgSelectRef.current?.onOrgSearchSelectReset();
        if (!isNeedReSearch) {
            this.handleConfirmClick([], []);
        }
        this.setState({
            inputValue: '',
            searchedIds: [],
            selectedIds: [],
            isOrgDidMount: false, //防止在别处请求中请求
            isConfirm: false,
        }, async () => {
            if (resetIds && isNeedReSearch) {
                this.setState({ loading: true });
                await this.getOrgOptionsById({ ids: resetIds, isDidMount: true });
                this.setState({ loading: false });
            }
        });
    }

    onOrgConfirm = () => {
        this.citySelectorRef.current?.submit();
    }

    handleConfirmClick = (value: any[], options: any) => {
        const { onConfirm, multiple, maxNum, showSearch } = this.props;
        const findSubmitName = (options: any) => options?.map((item: any) => item.name);
        let inputValue = '';
        const nameOptions = findSubmitName(options);
        if (multiple) {
            inputValue = maxNum && nameOptions.length > maxNum ? `${locale.lng('OrganizationPicker.chosen')}${nameOptions.length}${locale.lng('OrganizationPicker.items')}` : nameOptions.join();
        } else {
            inputValue = nameOptions.join();
        }
        this.setState({
            inputValue,
            searchedIds: [], //点击确定后将searchedId清空,全部存到selectedId中
            selectedIds: value,
            isConfirm: false,
            showPopover: false,
        }, () => {
            if (isFunction(onConfirm)) {
                onConfirm(multiple ? value : value[0], options);
            }
        });
        //如果显示搜索框,将搜索框数据清空
        if (showSearch) {
            this.orgSelectRef.current?.onOrgSearchSelectReset();
        }
    }

    // handleFocus = () => {
    //     this.setState({
    //         showPopover: true,
    //     });
    // }

    handleCancelClick = () => {
        this.setState({
            showPopover: false,
        });
    }

    // handleBtnStatusChange = (status: boolean) => {
    //     this.setState({
    //         btnDisabled: status,
    //     });
    // }

    transformMinLevelData = (data: any, _minLevel: number) => data?.map(((orgOption: any) => {
        if (orgOption.childrenList && orgOption.childrenList.length !== 0) {
            orgOption.childrenList = this.transformMinLevelData(orgOption.childrenList, _minLevel);
        }
        orgOption.level -= _minLevel;
        return orgOption;
    }))

    changeDefaultLevelIdSearchResult = (data: any, newSearchResult: any[]) => {
        const { defaultLevelId } = this.props;
        data.forEach((item: any) => {
            if (`${item.parentId}` === `${defaultLevelId}`) {
                newSearchResult.push(item);
            } else {
                this.changeDefaultLevelIdSearchResult(item.childrenList ?? [], newSearchResult);
            }
        });
        return newSearchResult;
    }

    getOrgOptionsById = async (param: { ids?: Array<number>, parentId?: number, level?: number, isDidMount?: boolean, isSearched?: boolean, onMountedFetchFn?: (dataList: Array<object>) => void }) => {
        const { pidUrl, method, config, onFail, params, transformDataList, multiple, pidService, defaultLevelId, asyncFetchTree } = this.props;
        const { minLevel } = this.state;
        const { parentId, level, isDidMount, isSearched, onMountedFetchFn } = param;
        let { ids } = param;
        if (!ids) {
            ids = [];
        }
        const isPidServiceExist = pidService && isFunction(pidService);
        const newParams = {
            ...params,
            parentId: parentId || defaultLevelId || 0,
            level: level,
            orgIds: ids?.join('_'),
        };
        let result = {} as any;
        try {
            result = isPidServiceExist ? await pidService!({ ...newParams, value: ids }) : await axios({
                url: pidUrl,
                method,
                params: newParams,
                ...config,
            });
        } catch(e){

        }
        //初始化或重置请求
        const { data, code } = result.data ?? {};
        if (code !== 0) {
            if (isFunction(onFail)) {
                onFail('fail', result.data);
            }
        } else {
            const states: any = {};
            let dataList = data.list;
            if (defaultLevelId && ids.length) {
                dataList = this.changeDefaultLevelIdSearchResult(dataList, []);
            }

            if (transformDataList && isFunction(transformDataList)) {
                dataList =await Promise.resolve(transformDataList(dataList));
            }

            let _minLevel: any = minLevel;
            if (_minLevel === null && dataList?.length) {
                _minLevel = dataList[0].level - 1;
                states.minLevel = _minLevel;
            }
            dataList = this.transformMinLevelData(dataList, _minLevel);

            dataList = this.processingOrgData(dataList);
            if (isDidMount) {
                states.organizationOptions = dataList.map((item: any) => {
                    const { childrenList, ...resItem } = item;
                    //如果asyncFetchTree为false, 则将所有数据都返回
                    const newItem = !asyncFetchTree ? item : resItem;
                    //time: 重置时用来判断是否为新数据
                    return { ...newItem, time: new Date().getTime() };
                });
                states.inputValue = this.initDefaultValue(dataList, ids);
                if (ids.length) {
                    states.searchedValueOptions = dataList;
                }
                states.selectedIds = ids;
                states.isOrgDidMount = true;
                if (onMountedFetchFn) {
                    onMountedFetchFn(dataList);
                }
            }
            //搜索框变化的请求
            if (isSearched) {
                states.searchedValueOptions = cloneDeep(dataList);
                if (multiple) {
                    states.searchedIds = ids;
                } else {
                    states.selectedIds = ids;
                }
            }
            if (Object.keys(states).length) {
                this.setState(states);
            }
            return Promise.resolve(dataList);
        }
    }

    // findItemsByIds(orgItems: any[], ids: number[], parentItems: any[] = []) {
    //     let result: any[] = [];
    //     orgItems.forEach((item) => {
    //         const newItem: any = { ...item, childrenList: null };
    //         const newParentItems = [...parentItems, newItem];
    //         if (ids.includes(item.id)) {
    //             let currentLevel = result;
    //             parentItems?.forEach((parentItem) => {
    //                 const existingItem = currentLevel.find((i) => i.id === parentItem.id);
    //                 if (existingItem) {
    //                     currentLevel = existingItem.childrenList!;
    //                 } else {
    //                     parentItem.childrenList = [];
    //                     currentLevel.push(parentItem);
    //                     currentLevel = parentItem.childrenList;
    //                 }
    //             });
    //             currentLevel.push(newItem);
    //         }
    //         if (item.childrenList) {
    //             const childResult = this.findItemsByIds(item.childrenList, ids, newParentItems);
    //             if (childResult) {
    //                 result = result.concat(childResult);
    //             }
    //         }
    //     });
    //     return result.length > 0 ? result : null;
    // }

    processingOrgData = (data: any) => {
        let newOrgOptions: any = cloneDeep(data);
        const {
            // enableLevel,
            filterFn } = this.props;
        newOrgOptions = newOrgOptions?.map(((orgOption: any) => {
            if (orgOption.childrenList && orgOption.childrenList.length !== 0) {
                orgOption.childrenList = this.processingOrgData(orgOption.childrenList);
            }
            // //如果有enableLevel属性,则判断哪些禁选
            // if (enableLevel && isArray(enableLevel)) {
            //     if (enableLevel.includes(orgOption.level)) {
            //         orgOption.disabled = false;
            //     } else {
            //         orgOption.disabled = true;
            //     }
            // }
            //如果有filterFn,判断哪些需要过滤
            if (filterFn && isFunction(filterFn) && !!filterFn(orgOption)) {
                orgOption = null;
            }
            return orgOption;
        }));
        //将空item过滤
        if (filterFn && isFunction(filterFn)) {
            newOrgOptions = newOrgOptions?.filter((item: any) => item);
        }
        return newOrgOptions;
    }

    initDefaultValue = (list: Array<object>, ids: number[]) => {
        const { multiple, maxNum } = this.props;
        let name: string[] = [];
        let inputValue = '';
        name = this.findName(list, ids, name);
        if (multiple) {
            inputValue = maxNum && name.length > maxNum ? `${locale.lng('OrganizationPicker.chosen')}${name.length}${locale.lng('OrganizationPicker.items')}` : name.join();
        } else {
            inputValue = name.join();
        }
        return inputValue;
    }

    findName = (list: Array<object>, ids: number[], name: Array<string>) => {
        const { multiple } = this.props;
        const length = multiple ? ids.length : 1;
        list.forEach((item: any) => {
            if (ids.includes(item.id)) {
                name.push(item.name);
            }
            if (name.length !== length && item.childrenList) {
                name = this.findName(item.childrenList, ids, name);
            }
        });
        return name;
    }

    handleSearchChange = async (ids: Array<number>) => {
        const isIds = ids && ids.length;
        if (isIds) {
            this.setState({ loading: true, showPopover: true });
            await this.getOrgOptionsById({ ids, isSearched: true });
            this.setState({ loading: false, showPopover: true });
        } else {
            this.setState({
                searchedIds: [],
                showPopover: true
            });
        }
    }

    handleValueChange = (value: number[]) => {
        const { searchedIds } = this.state;
        this.setState({
            //判断搜索框中有没有已选中的id,有的话过滤
            selectedIds: difference(value, searchedIds),
        });
    }

    handleClickOutside = (event: MouseEvent) => {
        if (this.selectRef && this.selectRef.contains(event.target as HTMLElement)) {
            return;
        }

        this.setState({
            showPopover: false,
        });
    }

    render() {
        const {
            className,
            style,
            popupClass,
            popupStyle,
            size = this.context.size || 'normal',
            styleType,
            disabled,
            placeholder,
            searchPlaceholder,
            multiple,
            searchUrl,
            params,
            method,
            config,
            onFail,
            showSearch,
            cascade,
            flip,
            placement,
            allowClear,
            onAllowClear,
            // enableLevel,
            filterFn,
            filterDisableFn,
            resetDisabled,
            disablePortal,
            zIndex,
            popupContainer,
            defaultLevelId,
            onDelete,
            searchDisablePortal,
            asyncFetchTree,
            searchTransformData,
            renderMenu,
            renderMenuItem,
            onValueChange,
        } = this.props;
        const {
            inputValue,
            btnDisabled,
            selectedIds,
            searchedIds,
            searchedValueOptions,
            organizationOptions,
            loading,
            showPopover,
            minLevel,
        } = this.state;
        const citySelectorValue = multiple ? [...selectedIds, ...searchedIds] : selectedIds;

        return (
            <Manager>
                <Reference
                    innerRef={(node) => { this.selectRef = node; }}
                >
                    <div
                        onClick={() => {
                            if (!disabled) {
                                this.setState({
                                    showPopover: true,
                                });
                            }
                        }}
                    >
                        <Input
                            size={size}
                            styleType={styleType}
                            disabled={disabled}
                            placeholder={placeholder ?? locale.lng('OrganizationPicker.chose')}
                            value={inputValue}
                            inputRef={this.inputRef}
                            allowClear={allowClear}
                            onAllowClear={(e) => {
                                if (!allowClear) {
                                    return;
                                }
                                this.handleResetClick();
                                onAllowClear && onAllowClear(e);
                                e.stopPropagation();
                            }}
                        />
                    </div>
                </Reference>
                <PopperPortal
                    lazy
                    zIndex={zIndex}
                    visible
                    container={popupContainer}
                    disablePortal={disablePortal}
                    modifiers={[{
                        name: 'flip',
                        enabled: flip,
                        options: {
                            flipVariations: flip === 'flip',
                        },
                    }]}
                    isCloseMotion={false}
                    onClickOutside={this.handleClickOutside}
                    placement={this.getPlacement()}
                    className={classnames(`${this.context.prefixCls}-org-popperportal`, {
                        [`${this.context.prefixCls}-org-hidden`]: !showPopover,
                    }, { className })}
                >
                    <div
                        className={`${this.context.prefixCls}-organizationPicker ${className}`}
                        style={style}
                    >
                        <div
                            className={`${this.context.prefixCls}-org-box ${popupClass}`}
                            style={popupStyle}
                        >
                            {
                                showSearch && (
                                    <>
                                        {/* 搜索组织结构 */}
                                        <div className={`${this.context.prefixCls}-org-title`}>{locale.lng('OrganizationPicker.search')}</div>
                                        <OrgSelect
                                            ref={this.orgSelectRef}
                                            multiple={multiple}
                                            params={params}
                                            selectedData={searchedIds}
                                            searchUrl={searchUrl}
                                            method={method}
                                            config={config}
                                            handleSearchChange={this.handleSearchChange}
                                            onFail={onFail}
                                            // enableLevel={enableLevel}
                                            filterFn={filterFn}
                                            filterDisableFn={filterDisableFn}
                                            citySelectorValue={citySelectorValue}
                                            cascade={cascade}
                                            defaultLevelId={defaultLevelId}
                                            minLevel={minLevel}
                                            disablePortal={searchDisablePortal}
                                            searchTransformData={searchTransformData}
                                            placeholder={searchPlaceholder}
                                        />
                                    </>
                                )
                            }
                            <div className={`${this.context.prefixCls}-org-title`}>{locale.lng('OrganizationPicker.chose')}</div>
                            <CitySelector
                                options={organizationOptions}
                                layout="cascade"
                                inline
                                loading={loading}
                                multiple={multiple}
                                ref={this.citySelectorRef}
                                values={citySelectorValue}
                                fieldNames={{ label: 'name', value: 'id', children: 'childrenList' }}
                                loadData={asyncFetchTree ? this.fetchData : undefined}
                                onChange={(value, options) => {
                                    this.handleConfirmClick(value, options);
                                }}
                                onValueChange={(value: number[], options: any[], tabIndex: number) => {
                                    if (multiple) {
                                        this.handleValueChange(value);
                                    }
                                    if (isFunction(onValueChange)) {
                                        onValueChange(value, options, tabIndex);
                                    }
                                }}
                                filterDisableFn={filterDisableFn}
                                valueOptions={searchedValueOptions}
                                cascade={cascade}
                                onDelete={onDelete}
                                needCompareOptions
                                renderMenu={renderMenu}
                                renderMenuItem={renderMenuItem}
                                alwaysValueChange
                            />
                            <div className={`${this.context.prefixCls}-text-right`}>
                                <Button
                                    disabled={btnDisabled}
                                    className="m-right-sm"
                                    type="hollow"
                                    onClick={this.handleCancelClick}
                                >{locale.lng('OrganizationPicker.cancel')}</Button>
                                <Button
                                    disabled={btnDisabled || resetDisabled}
                                    className="m-right-sm"
                                    type="hollow"
                                    onClick={() => {
                                        this.handleResetClick(true);
                                    }}
                                >{locale.lng('OrganizationPicker.reset')}</Button>
                                <Button
                                    disabled={btnDisabled}
                                    className="m-right-sm"
                                    onClick={this.onOrgConfirm}
                                >{locale.lng('OrganizationPicker.confirm')}</Button>
                            </div>
                        </div>
                    </div>
                </PopperPortal>
            </Manager>
        );
    }
}

export default OrganizationSelector;
